const mongoConnections = require("../connections/mongo")
const mongoose = require('mongoose')
const Schema = mongoose.Schema

const FileSchema = new mongoose.Schema(
  {
    telegramFileId: {
      type: String,
      required: true,
      index: true
    },
    originalFileName: {
      type: String,
      required: true
    },
    fileSize: {
      type: Number,
      required: true
    },
    mimeType: {
      type: String,
      required: true
    },
    uploadDate: {
      type: Date,
      default: Date.now
    },
    parentId: {
      type: Schema.Types.ObjectId,
      ref: 'Folder',
      default: null,
      index: true
    },
    isDeleted: {
      type: Boolean,
      default: false,
      index: true
    },
    deletedAt: {
      type: Date,
      default: null
    },
    permanentlyDeletedAt: {
      type: Date,
      default: null
    },
    ownerId: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      default: null,
      index: true
    },
    isProcessingAI: {
      type: Boolean,
      default: false
    },
    aiMetadata: {
      type: Schema.Types.Mixed,
      default: {}
    },
    // Full Telegram API response data
    telegramMetadata: {
      type: Schema.Types.Mixed,
      default: {}
    },
    // Additional file metadata
    fileMetadata: {
      width: { type: Number }, // for images/videos
      height: { type: Number }, // for images/videos
      duration: { type: Number }, // for videos/audio
      thumbnail: { type: String }, // thumbnail file_id if available
      fileUniqueId: { type: String }, // Telegram's unique file identifier
      fileName: { type: String }, // filename from Telegram (may differ from originalFileName)
      performer: { type: String }, // for audio files
      title: { type: String }, // for audio files
      type: Schema.Types.Mixed // additional type-specific metadata
    },
    // File hash for duplicate detection
    fileHash: {
      md5: { type: String },
      sha256: { type: String }
    },
    // Upload session metadata
    uploadMetadata: {
      // Client information
      ipAddress: { type: String },
      userAgent: { type: String },
      referer: { type: String },

      // Geolocation (if provided by client)
      geolocation: {
        latitude: { type: Number },
        longitude: { type: Number },
        accuracy: { type: Number },
        timestamp: { type: Date },
        address: { type: String }, // Reverse geocoded address
        city: { type: String },
        country: { type: String }
      },

      // Device/Browser information
      deviceInfo: {
        platform: { type: String }, // Windows, macOS, Linux, iOS, Android
        browser: { type: String }, // Chrome, Firefox, Safari, etc.
        browserVersion: { type: String },
        isMobile: { type: Boolean },
        isTablet: { type: Boolean },
        screenResolution: { type: String }, // "1920x1080"
        timezone: { type: String }, // "Asia/Ho_Chi_Minh"
        language: { type: String } // "vi-VN"
      },

      // Upload session details
      uploadSession: {
        sessionId: { type: String }, // Unique session identifier
        uploadStartTime: { type: Date },
        uploadEndTime: { type: Date },
        uploadDuration: { type: Number }, // in milliseconds
        uploadSpeed: { type: Number }, // bytes per second
        retryCount: { type: Number, default: 0 },
        uploadSource: { type: String, enum: ['web', 'mobile', 'api', 'drag-drop', 'paste'], default: 'web' }
      },

      // Network information
      networkInfo: {
        connectionType: { type: String }, // wifi, cellular, ethernet, etc.
        downlink: { type: Number }, // effective bandwidth estimate
        effectiveType: { type: String }, // slow-2g, 2g, 3g, 4g
        rtt: { type: Number } // round trip time
      }
    },
    // EXIF data from images/videos
    exifData: {
      // Camera information
      camera: {
        make: { type: String },
        model: { type: String },
        software: { type: String },
        lens: { type: String },
        serialNumber: { type: String }
      },
      // Photo settings
      photoSettings: {
        iso: { type: Number },
        fNumber: { type: Number },
        exposureTime: { type: Number },
        focalLength: { type: Number },
        focalLengthIn35mm: { type: Number },
        flash: { type: Schema.Types.Mixed },
        whiteBalance: { type: Schema.Types.Mixed },
        meteringMode: { type: Number },
        exposureMode: { type: Number },
        sceneCaptureType: { type: Number }
      },
      // Date and time from EXIF
      dateTime: {
        dateTimeOriginal: { type: Date },
        dateTimeDigitized: { type: Date },
        modifyDate: { type: Date },
        createDate: { type: Date },
        subsecTimeOriginal: { type: String },
        subsecTimeDigitized: { type: String }
      },
      // GPS from EXIF
      gps: {
        latitude: { type: Number },
        longitude: { type: Number },
        altitude: { type: Number },
        altitudeRef: { type: Number },
        speed: { type: Number },
        speedRef: { type: String },
        direction: { type: Number },
        directionRef: { type: String },
        timestamp: { type: String },
        datestamp: { type: String },
        gpsProcessingMethod: { type: String },
        gpsAreaInformation: { type: String }
      },
      // Image technical details
      image: {
        width: { type: Number },
        height: { type: Number },
        orientation: { type: Number },
        colorSpace: { type: Number },
        compression: { type: Number },
        bitsPerSample: { type: Schema.Types.Mixed },
        photometricInterpretation: { type: Number }
      },
      // Video metadata
      video: {
        duration: { type: Number },
        frameRate: { type: Number },
        bitrate: { type: Number },
        codec: { type: String },
        audioCodec: { type: String }
      },
      // Additional metadata
      additional: {
        artist: { type: String },
        copyright: { type: String },
        description: { type: String },
        userComment: { type: String },
        keywords: { type: Schema.Types.Mixed },
        subject: { type: String },
        title: { type: String }
      },
      // Raw EXIF for debugging
      raw: { type: Schema.Types.Mixed }
    },
    // Additional image metadata from Sharp
    imageMetadata: {
      format: { type: String },
      width: { type: Number },
      height: { type: Number },
      channels: { type: Number },
      depth: { type: String },
      density: { type: Number },
      hasProfile: { type: Boolean },
      hasAlpha: { type: Boolean },
      isProgressive: { type: Boolean },
      compression: { type: String },
      resolutionUnit: { type: String },
      size: { type: Number }
    }
  },
  {
    id: false,
    versionKey: false,
    timestamps: false
  }
)

// Indexes for better query performance
FileSchema.index({ parentId: 1, isDeleted: 1 })
FileSchema.index({ originalFileName: 1, isDeleted: 1 })
FileSchema.index({ uploadDate: -1 })
FileSchema.index({ ownerId: 1, isDeleted: 1 })

// Indexes for new metadata fields
FileSchema.index({ 'fileHash.md5': 1 })
FileSchema.index({ 'fileHash.sha256': 1 })
FileSchema.index({ 'uploadMetadata.ipAddress': 1 })
FileSchema.index({ 'uploadMetadata.uploadSession.sessionId': 1 })
FileSchema.index({ 'uploadMetadata.geolocation.city': 1 })
FileSchema.index({ 'uploadMetadata.deviceInfo.platform': 1 })

// Indexes for EXIF data
FileSchema.index({ 'exifData.camera.make': 1 })
FileSchema.index({ 'exifData.camera.model': 1 })
FileSchema.index({ 'exifData.dateTime.dateTimeOriginal': 1 })
FileSchema.index({ 'exifData.gps.latitude': 1, 'exifData.gps.longitude': 1 })
FileSchema.index({ 'exifData.photoSettings.iso': 1 })
FileSchema.index({ 'imageMetadata.format': 1 })

module.exports = mongoConnections("master").model("File", FileSchema)
